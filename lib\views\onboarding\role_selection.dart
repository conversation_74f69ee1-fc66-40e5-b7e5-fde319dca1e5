import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gatepass_flutter/models/user_role.dart';

class RoleSelectionView extends StatefulWidget {
  const RoleSelectionView({super.key});

  @override
  State<RoleSelectionView> createState() => _RoleSelectionViewState();
}

class _RoleSelectionViewState extends State<RoleSelectionView> {
  UserRole? selectedRole;

  void _onStartPressed() {
    if (selectedRole == null) return;
    switch (selectedRole) {
      case UserRole.customer:
        context.go('/login/customer');
        break;
      case UserRole.promoter:
        context.go('/login/promoter');
        break;
      case UserRole.admin:
        context.go('/login/admin');
        break;
      default:
        break;
    }
  }

  Widget _roleCard({
    required UserRole role,
    required String title,
    required String subtitle,
    required String emoji,
  }) {
    final isSelected = selectedRole == role;
    return GestureDetector(
      onTap: () => setState(() => selectedRole = role),
      child: Container(
        width: double.infinity,
        height: 120,
        margin: const EdgeInsets.symmetric(vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0xFF6B46C1),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: isSelected ? const Color(0xFF4C1D95) : Colors.transparent,
            width: 2,
          ),
        ),
        child: Stack(
          children: [
            // Character illustration on the left
            Positioned(
              left: 20,
              top: 10,
              bottom: 10,
              child: Container(
                width: 100,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(emoji, style: const TextStyle(fontSize: 40)),
                ),
              ),
            ),
            // Text content on the right
            Positioned(
              right: 20,
              top: 0,
              bottom: 0,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.white70,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F3F0),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
          child: Column(
            children: [
              // Decorative stars at top
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Transform.rotate(
                    angle: 0.3,
                    child: const Icon(
                      Icons.star_outline,
                      color: Color(0xFF7B68EE),
                      size: 24,
                    ),
                  ),
                  Transform.rotate(
                    angle: -0.3,
                    child: const Icon(
                      Icons.star_outline,
                      color: Color(0xFFFF6B9D),
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 40),

              // Title
              const Text(
                'Choose your role\nbelow',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w800,
                  color: Color(0xFF2D2D2D),
                  height: 1.3,
                ),
              ),
              const SizedBox(height: 20),

              // Curved arrow
              Transform.rotate(
                angle: 0.2,
                child: const Icon(
                  Icons.south_east_rounded,
                  size: 28,
                  color: Color(0xFF2D2D2D),
                ),
              ),
              const SizedBox(height: 40),

              // Admin role card
              _roleCard(
                role: UserRole.admin,
                title: 'Admin',
                subtitle: 'System Management',
                emoji: '👨‍💼',
              ),

              const SizedBox(height: 20),

              // "or" text
              const Text(
                'or',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF6B7280),
                ),
              ),

              const SizedBox(height: 20),

              // Customer role card
              _roleCard(
                role: UserRole.customer,
                title: 'Customer',
                subtitle: 'Service Access',
                emoji: '👤',
              ),

              const SizedBox(height: 20),

              // "or" text
              const Text(
                'or',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF6B7280),
                ),
              ),

              const SizedBox(height: 20),

              // Promoter role card
              _roleCard(
                role: UserRole.promoter,
                title: 'Promoter',
                subtitle: 'Event Promotion',
                emoji: '📢',
              ),

              const Spacer(),

              // Decorative stars at bottom
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Transform.rotate(
                    angle: -0.2,
                    child: const Icon(
                      Icons.star_outline,
                      color: Color(0xFFFFB347),
                      size: 18,
                    ),
                  ),
                  Transform.rotate(
                    angle: 0.4,
                    child: const Icon(
                      Icons.star_outline,
                      color: Color(0xFF7B68EE),
                      size: 22,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Get started button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: selectedRole != null ? _onStartPressed : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2D2D2D),
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: Colors.grey.shade300,
                    disabledForegroundColor: Colors.grey.shade600,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        "Get started",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward, size: 18),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
