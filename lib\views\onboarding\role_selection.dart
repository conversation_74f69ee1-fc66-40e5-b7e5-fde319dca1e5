import 'package:flutter/material.dart';
import 'package:gatepass_flutter/components/common_image_view.dart';
import 'package:go_router/go_router.dart';
import 'package:gatepass_flutter/models/user_role.dart';
import 'package:gatepass_flutter/utils/assets.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RoleSelectionView extends StatefulWidget {
  const RoleSelectionView({super.key});

  @override
  State<RoleSelectionView> createState() => _RoleSelectionViewState();
}

class _RoleSelectionViewState extends State<RoleSelectionView> {
  UserRole? selectedRole;

  void _onStartPressed() {
    if (selectedRole == null) return;
    switch (selectedRole) {
      case UserRole.customer:
        context.go('/login/customer');
        break;
      case UserRole.promoter:
        context.go('/login/promoter');
        break;
      case UserRole.admin:
        context.go('/login/admin');
        break;
      default:
        break;
    }
  }

  Widget _roleCard({
    required UserRole role,
    required String title,
    required String svgAsset,
  }) {
    final isSelected = selectedRole == role;
    return GestureDetector(
      onTap: () => setState(() => selectedRole = role),
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 40),
            width: double.infinity,
            height: 130,
            padding: const EdgeInsets.only(left: 120, right: 20),
            decoration: BoxDecoration(
              color: const Color(0xFFD9C4F0),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? Colors.black : Colors.transparent,
                width: 2,
              ),
            ),
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          Positioned(
            left: 20,
            top: 0,
            child: SizedBox(
              width: 120,
              height: 120,
              child: CommonImageView(imagePath: svgAsset),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F4F2),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Text(
                'Choose your role\nbelow',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 28, fontWeight: FontWeight.w800),
              ),
              const SizedBox(height: 16),
              const Icon(Icons.arrow_downward, size: 24),
              const SizedBox(height: 32),
              _roleCard(
                role: UserRole.customer,
                title: 'Customer',
                svgAsset: Assets.user,
              ),
              const SizedBox(height: 24),
              _roleCard(
                role: UserRole.promoter,
                title: 'Promoter',
                svgAsset: Assets.promoter,
              ),
              const SizedBox(height: 24),
              _roleCard(
                role: UserRole.admin,
                title: 'Admin',
                svgAsset: Assets.admin,
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _onStartPressed,
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text("Get started"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    textStyle: const TextStyle(fontSize: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(14),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
